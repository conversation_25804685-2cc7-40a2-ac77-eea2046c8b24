#!/usr/bin/env python3
"""
Test script to verify the BGL log anomaly detection implementation.

This script tests the core functionality without requiring API keys.
"""

import os
import sys
from typing import List

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from bgl_processor import BGLProcessor, WindowType, LogEntry, LogWindow


def test_log_entry_parsing():
    """Test LogEntry parsing functionality."""
    print("Testing LogEntry parsing...")
    
    # Test normal entry
    normal_line = "- 1117838570 2005.06.03 R02-M1-N0-C:J12-U11 2005-06-03-15.42.50.363779 R02-M1-N0-C:J12-U11 RAS KERNEL INFO instruction cache parity error corrected"
    normal_entry = LogEntry.from_line(normal_line)
    
    assert normal_entry.is_normal == True, "Normal entry should be marked as normal"
    assert "R02-M1-N0-C:J12-U11" in normal_entry.node_id, "Node ID should be parsed correctly"
    print("✓ Normal entry parsing works")
    
    # Test abnormal entry
    abnormal_line = "APPREAD 1117869872 2005.06.04 R23-M1-N8-I:J18-U11 2005-06-04-00.24.32.398284 R23-M1-N8-I:J18-U11 RAS APP FATAL ciod: failed to read message prefix"
    abnormal_entry = LogEntry.from_line(abnormal_line)
    
    assert abnormal_entry.is_normal == False, "Abnormal entry should be marked as abnormal"
    assert "APPREAD" in abnormal_entry.raw_line, "Raw line should contain APPREAD"
    print("✓ Abnormal entry parsing works")


def test_window_labeling():
    """Test window labeling logic."""
    print("\nTesting window labeling...")
    
    # Create test entries
    normal_entry1 = LogEntry.from_line("- 1117838570 2005.06.03 R02-M1-N0-C:J12-U11 2005-06-03-15.42.50.363779 R02-M1-N0-C:J12-U11 RAS KERNEL INFO test")
    normal_entry2 = LogEntry.from_line("- 1117838571 2005.06.03 R02-M1-N0-C:J12-U12 2005-06-03-15.42.51.363779 R02-M1-N0-C:J12-U12 RAS KERNEL INFO test2")
    abnormal_entry = LogEntry.from_line("APPREAD 1117869872 2005.06.04 R23-M1-N8-I:J18-U11 2005-06-04-00.24.32.398284 R23-M1-N8-I:J18-U11 RAS APP FATAL test")
    
    # Test all normal window
    normal_window = LogWindow(
        entries=[normal_entry1, normal_entry2],
        is_normal=True,
        window_id=0,
        start_index=0,
        end_index=1
    )
    assert normal_window.is_normal == True, "Window with all normal entries should be normal"
    print("✓ All normal window labeling works")
    
    # Test mixed window (should be abnormal)
    mixed_window = LogWindow(
        entries=[normal_entry1, abnormal_entry],
        is_normal=True,
        window_id=1,
        start_index=0,
        end_index=1
    )
    assert mixed_window.is_normal == False, "Window with any abnormal entry should be abnormal"
    print("✓ Mixed window labeling works")


def test_bgl_processor():
    """Test BGLProcessor functionality."""
    print("\nTesting BGLProcessor...")
    
    dataset_path = "datasets/BGL/BGL.log.deduplicated"
    if not os.path.exists(dataset_path):
        print(f"⚠️  Dataset not found at {dataset_path}, skipping processor tests")
        return
    
    processor = BGLProcessor(dataset_path)
    
    # Test loading
    processor.load_dataset(max_lines=1000)
    assert len(processor.log_entries) > 0, "Should load log entries"
    print(f"✓ Loaded {len(processor.log_entries)} log entries")
    
    # Test sliding windows
    sliding_windows = processor.create_windows(
        window_size=5,
        window_type=WindowType.SLIDING,
        step_size=2
    )
    assert len(sliding_windows) > 0, "Should create sliding windows"
    print(f"✓ Created {len(sliding_windows)} sliding windows")
    
    # Test fixed windows
    fixed_windows = processor.create_windows(
        window_size=10,
        window_type=WindowType.FIXED
    )
    assert len(fixed_windows) > 0, "Should create fixed windows"
    print(f"✓ Created {len(fixed_windows)} fixed windows")
    
    # Test statistics
    stats = processor.get_window_statistics()
    assert 'total_windows' in stats, "Should provide statistics"
    assert stats['total_windows'] == len(fixed_windows), "Statistics should match window count"
    print(f"✓ Statistics: {stats['total_windows']} total, {stats['normal_windows']} normal, {stats['abnormal_windows']} abnormal")
    
    # Test DataFrame export
    df = processor.export_windows_to_dataframe()
    assert len(df) == len(fixed_windows), "DataFrame should have same length as windows"
    print(f"✓ Exported to DataFrame with shape {df.shape}")
    
    return processor


def test_balanced_sampling():
    """Test balanced sampling functionality."""
    print("\nTesting balanced sampling...")
    
    dataset_path = "datasets/BGL/BGL.log.deduplicated"
    if not os.path.exists(dataset_path):
        print("⚠️  Dataset not found, skipping sampling tests")
        return
    
    processor = BGLProcessor(dataset_path)
    processor.load_dataset(max_lines=5000)  # Load more data to get abnormal entries
    processor.create_windows(window_size=8, window_type=WindowType.SLIDING, step_size=4)
    
    # Test balanced sampling
    normal_sample, abnormal_sample = processor.get_balanced_sample(n_samples=20)
    
    print(f"✓ Balanced sampling: {len(normal_sample)} normal, {len(abnormal_sample)} abnormal")

    if normal_sample:
        print(f"  Example normal (full): {' | '.join(normal_sample[0].get_messages()[:2])}...")
        print(f"  Example normal (clean): {' | '.join(normal_sample[0].get_clean_messages()[:2])}...")
    if abnormal_sample:
        print(f"  Example abnormal (full): {' | '.join(abnormal_sample[0].get_messages()[:2])}...")
        print(f"  Example abnormal (clean): {' | '.join(abnormal_sample[0].get_clean_messages()[:2])}...")


def test_dspy_imports():
    """Test DSPy-related imports."""
    print("\nTesting DSPy imports...")
    
    try:
        import dspy
        print("✓ DSPy import successful")
        
        from dspy_anomaly_agent import LogSequenceExample, LogAnomalySignature, LogAnomalyDetector
        print("✓ DSPy agent components import successful")
        
        # Test example creation
        example = LogSequenceExample(
            sequence="test sequence",
            label="normal",
            reasoning="test reasoning"
        )
        assert example.sequence == "test sequence", "Example creation should work"
        print("✓ LogSequenceExample creation works")
        
    except ImportError as e:
        print(f"⚠️  DSPy import failed: {e}")
        print("   Install with: pip install dspy-ai")


def main():
    """Run all tests."""
    print("BGL Log Anomaly Detection - Implementation Test")
    print("=" * 50)
    
    try:
        test_log_entry_parsing()
        test_window_labeling()
        test_bgl_processor()
        test_balanced_sampling()
        test_dspy_imports()
        
        print("\n" + "=" * 50)
        print("✅ All tests completed successfully!")
        print("\nNext steps:")
        print("1. Set OPENAI_API_KEY environment variable to test DSPy agent")
        print("2. Run 'python example_usage.py' for full demonstration")
        print("3. Use the components in your own code for log anomaly detection")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
