"""
BGL Dataset Processor with Sliding and Fixed Window Support

This module processes the BGL (BlueGene/L) dataset and creates windows of log entries
for anomaly detection. It supports both sliding and fixed window approaches.

Log entry format:
- Normal entries start with "- "
- Abnormal entries start with anything else (e.g., "APPREAD", "KERNEL", etc.)
"""

import pandas as pd
import numpy as np
from typing import List, Tuple, Dict, Union
from dataclasses import dataclass
from enum import Enum


class WindowType(Enum):
    SLIDING = "sliding"
    FIXED = "fixed"


@dataclass
class LogEntry:
    """Represents a single log entry from the BGL dataset."""
    raw_line: str
    is_normal: bool
    timestamp: str
    node_id: str
    message: str
    
    @classmethod
    def from_line(cls, line: str) -> 'LogEntry':
        """Parse a line from the BGL dataset into a LogEntry object."""
        line = line.strip()
        is_normal = line.startswith("- ")

        # Parse the line components
        parts = line.split(' ', 6)
        if is_normal and len(parts) >= 6:
            # Normal entry format: "- timestamp date node_id detailed_timestamp node_id_again message"
            timestamp = parts[2]  # date part
            node_id = parts[3]    # first node_id
            message = parts[6] if len(parts) > 6 else ""
        elif not is_normal and len(parts) >= 6:
            # Abnormal entry format: "LABEL timestamp date node_id detailed_timestamp node_id_again message"
            timestamp = parts[2]  # date part
            node_id = parts[3]    # first node_id
            message = parts[6] if len(parts) > 6 else ""
        else:
            # Fallback for malformed lines
            timestamp = ""
            node_id = ""
            message = line

        return cls(
            raw_line=line,
            is_normal=is_normal,
            timestamp=timestamp,
            node_id=node_id,
            message=message
        )


@dataclass
class LogWindow:
    """Represents a window of log entries with its label."""
    entries: List[LogEntry]
    is_normal: bool
    window_id: int
    start_index: int
    end_index: int
    
    def __post_init__(self):
        """Determine if the window is normal or abnormal based on its entries."""
        # If any entry in the window is abnormal, the entire window is abnormal
        self.is_normal = all(entry.is_normal for entry in self.entries)
    
    def get_messages(self) -> List[str]:
        """Get the messages from all entries in the window."""
        return [entry.message for entry in self.entries]

    def get_clean_messages(self) -> List[str]:
        """Get cleaned messages with only the essential log content."""
        messages = []
        for entry in self.entries:
            # Extract just the message part, removing redundant node/timestamp info
            message = entry.message.strip()
            if message:
                messages.append(message)
        return messages

    def get_raw_lines(self) -> List[str]:
        """Get the raw lines from all entries in the window."""
        return [entry.raw_line for entry in self.entries]


class BGLProcessor:
    """
    Processes BGL dataset and creates windows for anomaly detection.
    
    Supports both sliding and fixed window approaches:
    - Sliding window: overlapping windows that slide by step_size
    - Fixed window: non-overlapping consecutive windows
    """
    
    def __init__(self, dataset_path: str):
        """
        Initialize the BGL processor.
        
        Args:
            dataset_path: Path to the BGL dataset file
        """
        self.dataset_path = dataset_path
        self.log_entries: List[LogEntry] = []
        self.windows: List[LogWindow] = []
        
    def load_dataset(self, max_lines: int = None) -> None:
        """
        Load the BGL dataset from file.
        
        Args:
            max_lines: Maximum number of lines to load (None for all)
        """
        print(f"Loading BGL dataset from {self.dataset_path}...")
        
        with open(self.dataset_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
            
        if max_lines:
            lines = lines[:max_lines]
            
        self.log_entries = [LogEntry.from_line(line) for line in lines]
        
        normal_count = sum(1 for entry in self.log_entries if entry.is_normal)
        abnormal_count = len(self.log_entries) - normal_count
        
        print(f"Loaded {len(self.log_entries)} log entries:")
        print(f"  - Normal entries: {normal_count}")
        print(f"  - Abnormal entries: {abnormal_count}")
        
    def create_windows(self, 
                      window_size: int, 
                      window_type: WindowType = WindowType.SLIDING,
                      step_size: int = 1) -> List[LogWindow]:
        """
        Create windows from the loaded log entries.
        
        Args:
            window_size: Number of log entries per window
            window_type: Type of windowing (sliding or fixed)
            step_size: Step size for sliding window (ignored for fixed window)
            
        Returns:
            List of LogWindow objects
        """
        if not self.log_entries:
            raise ValueError("No log entries loaded. Call load_dataset() first.")
            
        if window_size <= 0:
            raise ValueError("Window size must be positive.")
            
        self.windows = []
        window_id = 0
        
        if window_type == WindowType.SLIDING:
            # Sliding window approach
            for i in range(0, len(self.log_entries) - window_size + 1, step_size):
                window_entries = self.log_entries[i:i + window_size]
                window = LogWindow(
                    entries=window_entries,
                    is_normal=True,  # Will be set in __post_init__
                    window_id=window_id,
                    start_index=i,
                    end_index=i + window_size - 1
                )
                self.windows.append(window)
                window_id += 1
                
        elif window_type == WindowType.FIXED:
            # Fixed window approach
            for i in range(0, len(self.log_entries), window_size):
                end_idx = min(i + window_size, len(self.log_entries))
                window_entries = self.log_entries[i:end_idx]
                
                # Skip incomplete windows at the end
                if len(window_entries) == window_size:
                    window = LogWindow(
                        entries=window_entries,
                        is_normal=True,  # Will be set in __post_init__
                        window_id=window_id,
                        start_index=i,
                        end_index=end_idx - 1
                    )
                    self.windows.append(window)
                    window_id += 1
        
        normal_windows = sum(1 for w in self.windows if w.is_normal)
        abnormal_windows = len(self.windows) - normal_windows
        
        print(f"Created {len(self.windows)} windows using {window_type.value} approach:")
        print(f"  - Normal windows: {normal_windows}")
        print(f"  - Abnormal windows: {abnormal_windows}")
        
        return self.windows
    
    def get_window_statistics(self) -> Dict[str, Union[int, float]]:
        """Get statistics about the created windows."""
        if not self.windows:
            return {}
            
        normal_count = sum(1 for w in self.windows if w.is_normal)
        abnormal_count = len(self.windows) - normal_count
        
        return {
            'total_windows': len(self.windows),
            'normal_windows': normal_count,
            'abnormal_windows': abnormal_count,
            'normal_ratio': normal_count / len(self.windows) if self.windows else 0,
            'abnormal_ratio': abnormal_count / len(self.windows) if self.windows else 0
        }
    
    def export_windows_to_dataframe(self) -> pd.DataFrame:
        """Export windows to a pandas DataFrame for analysis."""
        if not self.windows:
            return pd.DataFrame()
            
        data = []
        for window in self.windows:
            data.append({
                'window_id': window.window_id,
                'start_index': window.start_index,
                'end_index': window.end_index,
                'is_normal': window.is_normal,
                'label': 'normal' if window.is_normal else 'abnormal',
                'messages': ' | '.join(window.get_messages()),
                'raw_lines': ' | '.join(window.get_raw_lines())
            })
            
        return pd.DataFrame(data)
    
    def get_balanced_sample(self, n_samples: int = 1000) -> Tuple[List[LogWindow], List[LogWindow]]:
        """
        Get a balanced sample of normal and abnormal windows.
        
        Args:
            n_samples: Total number of samples to return (will be split equally)
            
        Returns:
            Tuple of (normal_windows, abnormal_windows)
        """
        if not self.windows:
            return [], []
            
        normal_windows = [w for w in self.windows if w.is_normal]
        abnormal_windows = [w for w in self.windows if not w.is_normal]
        
        samples_per_class = n_samples // 2
        
        # Sample with replacement if needed
        normal_sample = np.random.choice(
            normal_windows, 
            size=min(samples_per_class, len(normal_windows)), 
            replace=len(normal_windows) < samples_per_class
        ).tolist()
        
        abnormal_sample = np.random.choice(
            abnormal_windows, 
            size=min(samples_per_class, len(abnormal_windows)), 
            replace=len(abnormal_windows) < samples_per_class
        ).tolist()
        
        return normal_sample, abnormal_sample


if __name__ == "__main__":
    # Example usage
    processor = BGLProcessor("datasets/BGL/BGL.log")
    processor.load_dataset(max_lines=10000)  # Load first 10k lines for testing
    
    # Create sliding windows
    sliding_windows = processor.create_windows(
        window_size=10, 
        window_type=WindowType.SLIDING, 
        step_size=5
    )
    
    print("\nSliding window statistics:")
    stats = processor.get_window_statistics()
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    # Create fixed windows
    processor.create_windows(
        window_size=10, 
        window_type=WindowType.FIXED
    )
    
    print("\nFixed window statistics:")
    stats = processor.get_window_statistics()
    for key, value in stats.items():
        print(f"  {key}: {value}")
