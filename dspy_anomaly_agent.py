"""
DSPy Agent for Log Anomaly Detection using LabeledFewShot Learning

This module implements a DSPy agent that uses LabeledFewShot learning to classify
log sequences as normal or abnormal based on the BGL dataset.
"""

import dspy
from typing import List, Dict, Tuple, Optional
import random
from dataclasses import dataclass
from bgl_processor import LogWindow, BGLProcessor, WindowType


@dataclass
class LogSequenceExample:
    """Represents a training example for the DSPy agent."""
    sequence: str
    label: str  # "normal" or "abnormal"
    reasoning: str = ""


class LogAnomalySignature(dspy.Signature):
    """
    Signature for log anomaly detection task.

    Given a sequence of log messages, determine if the sequence represents
    normal system behavior or indicates an anomaly.
    """

    log_sequence = dspy.InputField(
        desc="A sequence of log messages from a system, separated by ' | '. "
             "Each message contains the core log content without timestamps or node IDs."
    )
    
    reasoning = dspy.OutputField(
        desc="Step-by-step reasoning about why this log sequence is normal or abnormal. "
             "Consider patterns, error messages, frequency of events, and system behavior indicators."
    )
    
    classification = dspy.OutputField(
        desc="Classification of the log sequence. Must be either 'normal' or 'abnormal'."
    )


class LogAnomalyDetector(dspy.Module):
    """
    DSPy module for detecting anomalies in log sequences using LabeledFewShot learning.
    """

    def __init__(self, examples: List[LogSequenceExample] = None):
        """
        Initialize the log anomaly detector.

        Args:
            examples: List of labeled examples for few-shot learning
        """
        super().__init__()

        # Convert examples to DSPy format
        dspy_examples = []
        if examples:
            for example in examples:
                dspy_examples.append(
                    dspy.Example(
                        log_sequence=example.sequence,
                        reasoning=example.reasoning,
                        classification=example.label
                    ).with_inputs("log_sequence")
                )

        # Use ChainOfThought with LabeledFewShot
        self.predictor = dspy.ChainOfThought(LogAnomalySignature)

        # Set the examples for few-shot learning
        if dspy_examples:
            # Use LabeledFewShot as a teleprompter to set examples
            from dspy.teleprompt import LabeledFewShot
            teleprompter = LabeledFewShot(k=min(len(dspy_examples), 8))
            self.predictor = teleprompter.compile(self.predictor, trainset=dspy_examples)
    
    def forward(self, log_sequence: str) -> dspy.Prediction:
        """
        Classify a log sequence as normal or abnormal.
        
        Args:
            log_sequence: String representation of log entries separated by ' | '
            
        Returns:
            DSPy prediction with reasoning and classification
        """
        return self.predictor(log_sequence=log_sequence)
    
    def predict(self, log_sequence: str) -> Tuple[str, str]:
        """
        Predict the classification of a log sequence.
        
        Args:
            log_sequence: String representation of log entries
            
        Returns:
            Tuple of (classification, reasoning)
        """
        prediction = self.forward(log_sequence)
        return prediction.classification, prediction.reasoning


class LogAnomalyAgent:
    """
    High-level agent for log anomaly detection using DSPy and BGL dataset.
    """
    
    def __init__(self, 
                 dataset_path: str,
                 language_model: str = "gpt-3.5-turbo",
                 api_key: Optional[str] = None):
        """
        Initialize the log anomaly agent.
        
        Args:
            dataset_path: Path to the BGL dataset
            language_model: Language model to use for DSPy
            api_key: API key for the language model (if required)
        """
        self.dataset_path = dataset_path
        self.processor = BGLProcessor(dataset_path)
        self.detector: Optional[LogAnomalyDetector] = None
        
        # Configure DSPy with the language model
        if language_model.startswith("openai"):
            if api_key:
                lm = dspy.LM(model=language_model, api_key=api_key)
            else:
                lm = dspy.LM(model=language_model, api_key=api_key)
        else:
            # For other models, you might need different configurations
            lm = dspy.LM(model=language_model, api_base="http://192.168.0.104:11434")
            
        dspy.configure(lm=lm)
    
    def prepare_training_data(self, 
                            window_size: int = 10,
                            max_examples: int = 100,
                            balance_classes: bool = True) -> List[LogSequenceExample]:
        """
        Prepare training examples from the BGL dataset.
        
        Args:
            window_size: Size of log windows to create
            max_examples: Maximum number of examples to generate
            balance_classes: Whether to balance normal/abnormal examples
            
        Returns:
            List of LogSequenceExample objects
        """
        print("Preparing training data...")
        
        # Load dataset and create windows
        self.processor.load_dataset(max_lines=50000)  # Limit for faster processing
        windows = self.processor.create_windows(window_size=window_size, window_type=WindowType.FIXED)
        
        # Separate normal and abnormal windows
        normal_windows = [w for w in windows if w.is_normal]
        abnormal_windows = [w for w in windows if not w.is_normal]
        
        examples = []
        
        if balance_classes:
            # Balance the classes
            examples_per_class = min(max_examples // 2, len(normal_windows), len(abnormal_windows))
            
            # Sample normal examples
            normal_sample = random.sample(normal_windows, examples_per_class)
            for window in normal_sample:
                sequence = " | ".join(window.get_clean_messages())
                examples.append(LogSequenceExample(
                    sequence=sequence,
                    label="normal",
                    reasoning="This sequence contains only routine system operations without error indicators."
                ))

            # Sample abnormal examples
            abnormal_sample = random.sample(abnormal_windows, examples_per_class)
            for window in abnormal_sample:
                sequence = " | ".join(window.get_clean_messages())
                examples.append(LogSequenceExample(
                    sequence=sequence,
                    label="abnormal",
                    reasoning="This sequence contains error messages or anomalous system behavior indicators."
                ))
        else:
            # Use all available examples up to max_examples
            all_windows = windows[:max_examples]
            for window in all_windows:
                sequence = " | ".join(window.get_clean_messages())
                label = "normal" if window.is_normal else "abnormal"
                reasoning = ("This sequence contains only routine system operations without error indicators."
                           if window.is_normal else
                           "This sequence contains error messages or anomalous system behavior indicators.")

                examples.append(LogSequenceExample(
                    sequence=sequence,
                    label=label,
                    reasoning=reasoning
                ))
        
        print(f"Prepared {len(examples)} training examples")
        return examples
    
    def train(self, 
              window_size: int = 10,
              max_examples: int = 50,
              balance_classes: bool = True) -> None:
        """
        Train the anomaly detector with labeled examples.
        
        Args:
            window_size: Size of log windows to create
            max_examples: Maximum number of training examples
            balance_classes: Whether to balance normal/abnormal examples
        """
        # Prepare training data
        examples = self.prepare_training_data(
            window_size=window_size,
            max_examples=max_examples,
            balance_classes=balance_classes
        )
        
        # Initialize the detector with examples
        self.detector = LogAnomalyDetector(examples)
        
        print(f"Training completed with {len(examples)} examples")
    
    def predict(self, log_sequence: str) -> Tuple[str, str, float]:
        """
        Predict if a log sequence is normal or abnormal.

        Args:
            log_sequence: String representation of log entries

        Returns:
            Tuple of (classification, reasoning, confidence)
        """
        if not self.detector:
            raise ValueError("Agent not trained. Call train() first.")

        try:
            classification, reasoning = self.detector.predict(log_sequence)
        except Exception as e:
            # Handle connection errors gracefully
            if "No route to host" in str(e) or "APIConnectionError" in str(e):
                print(f"Warning: Could not connect to language model. Using fallback prediction.")
            else:
                raise e

        # Simple confidence estimation based on reasoning length and keywords
        confidence = 0.5  # Base confidence
        if "error" in reasoning.lower() or "fail" in reasoning.lower():
            confidence += 0.2
        if "normal" in reasoning.lower() or "routine" in reasoning.lower():
            confidence += 0.1
        if len(reasoning) > 50:  # Longer reasoning might indicate higher confidence
            confidence += 0.1

        confidence = min(confidence, 1.0)

        return classification, reasoning, confidence
    
    def evaluate_on_test_data(self, 
                            test_window_size: int = 10,
                            test_samples: int = 100) -> Dict[str, float]:
        """
        Evaluate the trained model on test data.

        Args:
            test_window_size: Size of test windows
            test_samples: Number of test samples to evaluate

        Returns:
            Dictionary with evaluation metrics including:
            - accuracy: Overall accuracy
            - precision: Precision for abnormal class
            - recall: Recall for abnormal class
            - f1_score: F1-score for abnormal class
            - Confusion matrix components (TP, FP, TN, FN)
        """
        if not self.detector:
            raise ValueError("Agent not trained. Call train() first.")
        
        print("Evaluating on test data...")
        
        # Create test windows (different from training data)
        test_processor = BGLProcessor(self.dataset_path)
        test_processor.load_dataset(max_lines=20000)  # Different subset for testing
        test_windows = test_processor.create_windows(window_size=test_window_size, window_type=WindowType.FIXED)
        
        # Sample test data
        test_sample = random.sample(test_windows, min(test_samples, len(test_windows)))

        # Initialize confusion matrix components
        true_positives = 0   # Correctly predicted abnormal
        false_positives = 0  # Incorrectly predicted abnormal (actually normal)
        true_negatives = 0   # Correctly predicted normal
        false_negatives = 0  # Incorrectly predicted normal (actually abnormal)

        total_predictions = len(test_sample)
        failed_predictions = 0

        for window in test_sample:
            sequence = " | ".join(window.get_clean_messages())
            true_label = "normal" if window.is_normal else "abnormal"

            try:
                predicted_label, _, _ = self.predict(sequence)
                predicted_label = predicted_label.lower()

                # Update confusion matrix
                if true_label == "abnormal" and predicted_label == "abnormal":
                    true_positives += 1
                elif true_label == "normal" and predicted_label == "abnormal":
                    false_positives += 1
                elif true_label == "normal" and predicted_label == "normal":
                    true_negatives += 1
                elif true_label == "abnormal" and predicted_label == "normal":
                    false_negatives += 1

            except Exception as e:
                print(f"Warning: Prediction failed for one sample: {e}")
                failed_predictions += 1

        # Adjust total predictions for failed ones
        successful_predictions = total_predictions - failed_predictions

        # Calculate metrics
        accuracy = (true_positives + true_negatives) / successful_predictions if successful_predictions > 0 else 0

        precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0

        recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0

        f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0

        metrics = {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1_score,
            'true_positives': true_positives,
            'false_positives': false_positives,
            'true_negatives': true_negatives,
            'false_negatives': false_negatives,
            'total_predictions': total_predictions,
            'successful_predictions': successful_predictions,
            'failed_predictions': failed_predictions
        }

        print(f"Evaluation results:")
        print(f"  Accuracy: {accuracy:.3f}")
        print(f"  Precision: {precision:.3f}")
        print(f"  Recall: {recall:.3f}")
        print(f"  F1-Score: {f1_score:.3f}")
        print(f"  Confusion Matrix:")
        print(f"    True Positives (Abnormal->Abnormal): {true_positives}")
        print(f"    False Positives (Normal->Abnormal): {false_positives}")
        print(f"    True Negatives (Normal->Normal): {true_negatives}")
        print(f"    False Negatives (Abnormal->Normal): {false_negatives}")
        print(f"  Total samples: {total_predictions}, Successful: {successful_predictions}, Failed: {failed_predictions}")

        return metrics


if __name__ == "__main__":
    # Example usage
    import os

    # Check which dataset file exists
    dataset_path = None
    possible_paths = [
        "datasets/BGL/BGL.log",
    ]

    for path in possible_paths:
        if os.path.exists(path):
            dataset_path = path
            break

    if not dataset_path:
        print("Error: No BGL dataset found. Please ensure the dataset is available.")
        exit(1)

    print(f"Using dataset: {dataset_path}")
    agent = LogAnomalyAgent(dataset_path, language_model="ollama_chat/gpt-oss-20b-ctx32k:latest")

    # Train the agent with smaller parameters for testing
    print("Training agent...")
    try:
        agent.train(window_size=50, max_examples=8, balance_classes=True)

        # Evaluate the model
        print("\nEvaluating model...")
        metrics = agent.evaluate_on_test_data(test_samples=80)
        print(f"Evaluation metrics: {metrics}")

    except Exception as e:
        print(f"Error during training or prediction: {e}")
        import traceback
        traceback.print_exc()
