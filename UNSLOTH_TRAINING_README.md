# BGL Log Anomaly Detection with Unsloth

This directory contains a complete training pipeline for fine-tuning language models on BGL log data using Unsloth for efficient training.

## Overview

The system processes the BGL (BlueGene/L) dataset and fine-tunes a language model to classify log sequences as either "normal" or "anomaly". It uses Unsloth for efficient 4-bit quantized training with LoRA adapters.

## Files

- `unsloth_bgl_trainer.py` - Main training script with data processing and model training
- `unsloth_bgl_inference.py` - Inference script for using the trained model
- `run_training.py` - Simple command-line interface for training
- `requirements.txt` - Updated with unsloth dependencies

## Installation

1. Install the required dependencies:
```bash
pip install -r requirements.txt
```

Note: The unsloth installation may take some time as it compiles optimized kernels.

## Quick Start

### 1. Quick Training (for testing)
```bash
python run_training.py --quick
```

This will:
- Use 5,000 training samples
- Train for 100 steps
- Use smaller windows (size 5)
- Complete in ~10-15 minutes on a GPU

### 2. Full Training
```bash
python run_training.py
```

This will:
- Use 50,000 training samples
- Train for 1,000 steps
- Use window size of 10
- Take ~1-2 hours on a GPU

### 3. Custom Training
```bash
python run_training.py \
    --max_samples 20000 \
    --max_steps 500 \
    --window_size 8 \
    --learning_rate 1e-4 \
    --batch_size 4
```

## Model Testing

After training, test the model:

```bash
python unsloth_bgl_inference.py
```

This will:
- Load the trained model
- Run a demo with example log sequences
- Evaluate the model on test data

## Configuration Options

### Training Parameters

- `--max_samples`: Number of training examples (default: 50,000)
- `--window_size`: Size of log windows (default: 10)
- `--max_steps`: Training steps (default: 1,000)
- `--learning_rate`: Learning rate (default: 2e-4)
- `--batch_size`: Training batch size (default: 2)

### Model Parameters

- `--model_name`: Base model (default: "unsloth/llama-3.2-1b-instruct-bnb-4bit")
- `--max_seq_length`: Maximum sequence length (default: 2048)
- `--output_dir`: Output directory (default: "./bgl_anomaly_model")

## Data Format

The system processes BGL logs where:
- Lines starting with "-" are normal logs
- Lines starting with other prefixes (e.g., "APPREAD", "KERNEL") are anomalous logs

Example log entries:
```
- 1117838570 2005.06.03 R02-M1-N0-C:J12-U11 2005-06-03-15.42.50.363779 R02-M1-N0-C:J12-U11 RAS KERNEL INFO instruction cache parity error corrected
APPREAD 1117869872 2005.06.04 R23-M1-N8-I:J18-U11 2005-06-04-00.24.32.398284 R23-M1-N8-I:J18-U11 RAS APP FATAL ciod: failed to read message prefix on control stream
```

## Training Process

1. **Data Processing**: 
   - Loads BGL log file
   - Creates sliding windows of log entries
   - Labels windows as normal/anomaly based on content
   - Converts to instruction-following format

2. **Model Setup**:
   - Loads base model with 4-bit quantization
   - Adds LoRA adapters for efficient fine-tuning
   - Configures training parameters

3. **Training**:
   - Fine-tunes model on log classification task
   - Uses gradient accumulation for effective larger batch sizes
   - Saves checkpoints and best model

4. **Evaluation**:
   - Tests model on held-out validation data
   - Provides accuracy, precision, recall, and F1 metrics

## Hardware Requirements

- **GPU**: Recommended (NVIDIA GPU with 8GB+ VRAM)
- **RAM**: 16GB+ system RAM
- **Storage**: 10GB+ free space for model and data

## Expected Performance

With the default configuration:
- **Training time**: 1-2 hours on RTX 3080/4080
- **Memory usage**: ~6-8GB GPU VRAM
- **Expected accuracy**: 85-95% on BGL test data

## Troubleshooting

### CUDA Out of Memory
- Reduce `--batch_size` to 1
- Reduce `--max_seq_length` to 1024
- Use `--quick` mode for testing

### Slow Training
- Ensure you're using a GPU
- Check that CUDA is properly installed
- Consider using a smaller model like "unsloth/llama-3.2-1b"

### Poor Performance
- Increase `--max_samples` for more training data
- Increase `--max_steps` for longer training
- Adjust `--window_size` based on your log patterns

## Integration with Existing Code

The trained model can be integrated with your existing DSPy pipeline:

```python
from unsloth_bgl_inference import BGLAnomalyClassifier

# Load trained model
classifier = BGLAnomalyClassifier("./bgl_anomaly_model")

# Classify log messages
messages = ["RAS KERNEL INFO error", "RAS APP FATAL failure"]
classification, confidence = classifier.classify_messages(messages)
print(f"Classification: {classification} (confidence: {confidence})")
```

## Next Steps

1. **Experiment with different models**: Try larger models like Llama-3.2-3B for better performance
2. **Tune hyperparameters**: Adjust learning rate, batch size, and training steps
3. **Add more features**: Include temporal patterns, node information, or log frequency
4. **Deploy the model**: Create a REST API or integrate with your monitoring system

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review the unsloth documentation: https://github.com/unslothai/unsloth
3. Examine the training logs for specific error messages
