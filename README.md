# BGL Log Anomaly Detection with DSPy

This project implements a comprehensive log anomaly detection system using the BGL (BlueGene/L) dataset with both sliding and fixed window approaches, combined with a DSPy agent that uses LabeledFewShot learning for classification.

## Features

- **BGL Dataset Processing**: Load and process the BGL supercomputer log dataset
- **Flexible Windowing**: Support for both sliding and fixed window approaches
- **Automatic Labeling**: Labels determined by log entry initials ("- " for normal, others for abnormal)
- **DSPy Integration**: Uses DSPy framework with LabeledFewShot learning for classification
- **Comprehensive Analysis**: Statistics, evaluation metrics, and data export capabilities

## Installation

1. Clone this repository:
```bash
git clone <repository-url>
cd LogAnomaly2
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Set up your OpenAI API key (required for DSPy agent):
```bash
export OPENAI_API_KEY="your-api-key-here"
```

## Dataset

The project uses the BGL dataset, which should be placed in `datasets/BGL/BGL.log.deduplicated`. The dataset contains:
- Log entries from a BlueGene/L supercomputer system
- Normal entries starting with "- "
- Abnormal entries starting with other prefixes (e.g., "APPREAD", "KERNEL")

## Usage

### Basic Usage

```python
from bgl_processor import BGLProcessor, WindowType
from dspy_anomaly_agent import LogAnomalyAgent

# Process BGL dataset with sliding windows
processor = BGLProcessor("datasets/BGL/BGL.log.deduplicated")
processor.load_dataset(max_lines=10000)
windows = processor.create_windows(
    window_size=10, 
    window_type=WindowType.SLIDING, 
    step_size=5
)

# Train DSPy agent
agent = LogAnomalyAgent("datasets/BGL/BGL.log.deduplicated")
agent.train(window_size=10, max_examples=100, balance_classes=True)

# Make predictions
classification, reasoning, confidence = agent.predict("your log sequence here")
```

### Running the Example

```bash
python example_usage.py
```

This will demonstrate:
1. BGL dataset processing with different window types
2. DSPy agent training and prediction
3. Evaluation on test data
4. Sample predictions and statistics

## Components

### 1. BGL Processor (`bgl_processor.py`)

**Classes:**
- `LogEntry`: Represents a single log entry with parsing capabilities
- `LogWindow`: Represents a window of log entries with automatic labeling
- `BGLProcessor`: Main processor for creating windows and analyzing data

**Key Features:**
- Sliding window: Overlapping windows that slide by a specified step size
- Fixed window: Non-overlapping consecutive windows
- Automatic labeling based on log entry initials
- Statistics and data export capabilities

**Window Labeling Logic:**
- If ANY entry in a window is abnormal (doesn't start with "- "), the entire window is labeled as abnormal
- If ALL entries in a window are normal (start with "- "), the window is labeled as normal

**Message Processing:**
- `get_messages()`: Returns full log messages including all components
- `get_clean_messages()`: Returns only the essential message content, removing redundant timestamp and node information
- The DSPy agent uses clean messages for more focused classification on log content

### 2. DSPy Agent (`dspy_anomaly_agent.py`)

**Classes:**
- `LogAnomalySignature`: DSPy signature defining the classification task
- `LogAnomalyDetector`: DSPy module using LabeledFewShot learning
- `LogAnomalyAgent`: High-level agent combining BGL processing with DSPy

**Key Features:**
- LabeledFewShot learning with customizable examples
- Automatic training data preparation from BGL dataset
- Reasoning-based predictions with confidence estimation
- Evaluation capabilities on test data

### 3. Example Usage (`example_usage.py`)

Comprehensive demonstration script showing:
- BGL processor functionality
- DSPy agent training and prediction
- Sample data analysis
- Error handling and API key management

## Configuration

### Window Parameters

- `window_size`: Number of log entries per window
- `window_type`: `WindowType.SLIDING` or `WindowType.FIXED`
- `step_size`: Step size for sliding windows (ignored for fixed windows)

### Training Parameters

- `max_examples`: Maximum number of training examples
- `balance_classes`: Whether to balance normal/abnormal examples
- `language_model`: DSPy language model (default: "gpt-3.5-turbo")

## Example Output

```
Loading BGL dataset from datasets/BGL/BGL.log.deduplicated...
Loaded 10000 log entries:
  - Normal entries: 8234
  - Abnormal entries: 1766

Created 1996 windows using sliding approach:
  - Normal windows: 1456
  - Abnormal windows: 540

Training completed with 50 examples

Test Prediction:
Classification: abnormal
Reasoning: This sequence contains error messages indicating system failures...
Confidence: 0.85

Evaluation Results:
  accuracy: 0.820
  correct_predictions: 41
  total_predictions: 50
```

## API Reference

### BGLProcessor

```python
processor = BGLProcessor(dataset_path)
processor.load_dataset(max_lines=None)
windows = processor.create_windows(window_size, window_type, step_size=1)
stats = processor.get_window_statistics()
df = processor.export_windows_to_dataframe()
normal_sample, abnormal_sample = processor.get_balanced_sample(n_samples)
```

### LogAnomalyAgent

```python
agent = LogAnomalyAgent(dataset_path, language_model, api_key)
agent.train(window_size, max_examples, balance_classes)
classification, reasoning, confidence = agent.predict(log_sequence)
metrics = agent.evaluate_on_test_data(test_window_size, test_samples)
```

## Requirements

- Python 3.8+
- dspy-ai >= 2.4.0
- pandas >= 1.5.0
- numpy >= 1.21.0
- scikit-learn >= 1.0.0
- OpenAI API key (for DSPy agent)

## License

This project is provided as-is for educational and research purposes.

## Citation

If you use the BGL dataset, please cite:
- Adam J. Oliner, Jon Stearley. "What Supercomputers Say: A Study of Five System Logs", IEEE/IFIP International Conference on Dependable Systems and Networks (DSN), 2007.
- Jieming Zhu, et al. "Loghub: A Large Collection of System Log Datasets for AI-driven Log Analytics", IEEE International Symposium on Software Reliability Engineering (ISSRE), 2023.
