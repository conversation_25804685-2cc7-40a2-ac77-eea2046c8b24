#!/usr/bin/env python3
"""
Test script to demonstrate the clean messages functionality.

This script shows the difference between full log messages and clean messages
that contain only the essential log content for the DSPy agent.
"""

import os
import sys

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from bgl_processor import BGLProcessor, WindowType


def demonstrate_clean_messages():
    """Demonstrate the difference between full and clean messages."""
    print("Clean Messages Demonstration")
    print("=" * 50)
    
    dataset_path = "datasets/BGL/BGL.log.deduplicated"
    if not os.path.exists(dataset_path):
        print(f"Dataset not found at {dataset_path}")
        return
    
    # Load dataset and create windows
    processor = BGLProcessor(dataset_path)
    processor.load_dataset(max_lines=5000)
    windows = processor.create_windows(window_size=3, window_type=WindowType.SLIDING, step_size=20)
    
    # Get some normal and abnormal windows
    normal_windows = [w for w in windows if w.is_normal]
    abnormal_windows = [w for w in windows if not w.is_normal]
    
    print(f"\nFound {len(normal_windows)} normal and {len(abnormal_windows)} abnormal windows")
    
    # Show examples of normal windows
    if normal_windows:
        print(f"\n--- NORMAL WINDOW EXAMPLE ---")
        window = normal_windows[0]
        
        print(f"Full messages:")
        for i, msg in enumerate(window.get_messages()):
            print(f"  {i+1}. {msg}")
        
        print(f"\nClean messages:")
        for i, msg in enumerate(window.get_clean_messages()):
            print(f"  {i+1}. {msg}")
        
        print(f"\nCombined for DSPy agent:")
        print(f"  Full: {' | '.join(window.get_messages())}")
        print(f"  Clean: {' | '.join(window.get_clean_messages())}")
    
    # Show examples of abnormal windows
    if abnormal_windows:
        print(f"\n--- ABNORMAL WINDOW EXAMPLE ---")
        window = abnormal_windows[0]
        
        print(f"Full messages:")
        for i, msg in enumerate(window.get_messages()):
            print(f"  {i+1}. {msg}")
        
        print(f"\nClean messages:")
        for i, msg in enumerate(window.get_clean_messages()):
            print(f"  {i+1}. {msg}")
        
        print(f"\nCombined for DSPy agent:")
        print(f"  Full: {' | '.join(window.get_messages())}")
        print(f"  Clean: {' | '.join(window.get_clean_messages())}")
    
    # Show statistics
    print(f"\n--- STATISTICS ---")
    full_lengths = []
    clean_lengths = []
    
    sample_windows = windows[:100]  # Sample first 100 windows
    for window in sample_windows:
        full_seq = " | ".join(window.get_messages())
        clean_seq = " | ".join(window.get_clean_messages())
        full_lengths.append(len(full_seq))
        clean_lengths.append(len(clean_seq))
    
    avg_full = sum(full_lengths) / len(full_lengths) if full_lengths else 0
    avg_clean = sum(clean_lengths) / len(clean_lengths) if clean_lengths else 0
    
    print(f"Average sequence length (first 100 windows):")
    print(f"  Full messages: {avg_full:.1f} characters")
    print(f"  Clean messages: {avg_clean:.1f} characters")
    print(f"  Reduction: {((avg_full - avg_clean) / avg_full * 100):.1f}%")


def test_dspy_agent_with_clean_messages():
    """Test the DSPy agent with clean messages."""
    print(f"\n--- DSPY AGENT TEST ---")
    
    try:
        from dspy_anomaly_agent import LogAnomalyAgent
        
        dataset_path = "datasets/BGL/BGL.log.deduplicated"
        agent = LogAnomalyAgent(dataset_path, language_model="ollama_chat/gpt-oss:20b")
        
        print("Training agent with clean messages...")
        agent.train(window_size=3, max_examples=10, balance_classes=True)
        
        # Test with sample sequences
        test_sequences = [
            "instruction cache parity error corrected | performing bit sparing | generating core",
            "failed to read message prefix | FATAL error in application | system shutdown"
        ]
        
        for i, sequence in enumerate(test_sequences):
            print(f"\nTest {i+1}: {sequence}")
            try:
                classification, reasoning, confidence = agent.predict(sequence)
                print(f"  Classification: {classification}")
                print(f"  Confidence: {confidence:.2f}")
                print(f"  Reasoning: {reasoning[:100]}...")
            except Exception as e:
                print(f"  Error: {e}")
        
    except ImportError:
        print("DSPy agent not available (missing dependencies)")
    except Exception as e:
        print(f"Error testing DSPy agent: {e}")


def main():
    """Main function."""
    demonstrate_clean_messages()
    test_dspy_agent_with_clean_messages()
    
    print(f"\n--- SUMMARY ---")
    print("✓ Clean messages remove redundant timestamp and node information")
    print("✓ This makes the DSPy agent focus on actual log content")
    print("✓ Shorter sequences are more efficient for language models")
    print("✓ Classification becomes more focused on semantic content")


if __name__ == "__main__":
    main()
