"""
Unsloth Training Script for BGL Log Anomaly Detection

This script processes the BGL dataset and fine-tunes a language model using unsloth
for efficient log anomaly classification.
"""

import os
import json
import random
from typing import List, Dict, Tuple
from dataclasses import dataclass
import pandas as pd
from tqdm import tqdm

# Unsloth imports
from unsloth import FastLanguageModel
from unsloth import is_bfloat16_supported
import torch
from datasets import Dataset
from transformers import TrainingArguments
from trl import SFTTrainer

# Local imports
from bgl_processor import BGLProcessor, LogEntry, LogWindow


@dataclass
class TrainingConfig:
    """Configuration for the training process."""
    model_name: str = "unsloth/llama-3.2-1b-instruct-bnb-4bit"
    max_seq_length: int = 2048
    load_in_4bit: bool = True
    
    # LoRA parameters
    lora_r: int = 16
    lora_alpha: int = 16
    lora_dropout: float = 0.0
    bias: str = "none"
    use_gradient_checkpointing: str = "unsloth"
    
    # Training parameters
    per_device_train_batch_size: int = 2
    gradient_accumulation_steps: int = 4
    warmup_steps: int = 5
    max_steps: int = 1000
    learning_rate: float = 2e-4
    fp16: bool = not is_bfloat16_supported()
    bf16: bool = is_bfloat16_supported()
    logging_steps: int = 1
    optim: str = "adamw_8bit"
    weight_decay: float = 0.01
    lr_scheduler_type: str = "linear"
    seed: int = 3407
    
    # Data parameters
    max_samples: int = 50000  # Limit for initial training
    train_split: float = 0.8
    window_size: int = 10
    
    # Output
    output_dir: str = "./bgl_anomaly_model"


class BGLDatasetProcessor:
    """Processes BGL data for unsloth training."""
    
    def __init__(self, config: TrainingConfig):
        self.config = config
        self.processor = BGLProcessor()
    
    def create_instruction_format(self, messages: List[str], label: str) -> str:
        """Create instruction-following format for training."""
        log_sequence = " | ".join(messages)
        
        instruction = """You are an expert system administrator analyzing log messages for anomaly detection. 
Analyze the following sequence of log messages and classify it as either "normal" or "anomaly".

Consider these factors:
- Error messages and failure indicators
- Unusual patterns or frequencies
- System component failures
- Network or hardware issues

Log sequence: {log_sequence}

Classification:"""
        
        return instruction.format(log_sequence=log_sequence)
    
    def create_training_example(self, window: LogWindow) -> Dict[str, str]:
        """Create a training example from a log window."""
        messages = window.get_clean_messages()
        if not messages:
            return None
            
        label = "normal" if window.is_normal else "anomaly"
        instruction = self.create_instruction_format(messages, label)
        
        return {
            "instruction": instruction,
            "output": label
        }
    
    def process_bgl_data(self, file_path: str) -> List[Dict[str, str]]:
        """Process BGL log file and create training examples."""
        print("Loading BGL data...")
        
        # Load and process the data
        windows = self.processor.create_windows(
            file_path, 
            window_size=self.config.window_size,
            max_entries=self.config.max_samples * 2  # Load more to ensure we get enough after filtering
        )
        
        print(f"Created {len(windows)} windows")
        
        # Create training examples
        training_examples = []
        normal_count = 0
        anomaly_count = 0
        
        for window in tqdm(windows, desc="Creating training examples"):
            example = self.create_training_example(window)
            if example:
                training_examples.append(example)
                
                if window.is_normal:
                    normal_count += 1
                else:
                    anomaly_count += 1
                
                # Stop if we have enough examples
                if len(training_examples) >= self.config.max_samples:
                    break
        
        print(f"Created {len(training_examples)} training examples")
        print(f"Normal: {normal_count}, Anomaly: {anomaly_count}")
        
        # Balance the dataset if needed
        if anomaly_count > 0:
            ratio = normal_count / anomaly_count
            print(f"Normal to Anomaly ratio: {ratio:.2f}")
        
        return training_examples
    
    def create_datasets(self, training_examples: List[Dict[str, str]]) -> Tuple[Dataset, Dataset]:
        """Split data into train and validation datasets."""
        random.shuffle(training_examples)
        
        split_idx = int(len(training_examples) * self.config.train_split)
        train_data = training_examples[:split_idx]
        val_data = training_examples[split_idx:]
        
        print(f"Train samples: {len(train_data)}")
        print(f"Validation samples: {len(val_data)}")
        
        # Convert to HuggingFace datasets
        train_dataset = Dataset.from_list(train_data)
        val_dataset = Dataset.from_list(val_data)
        
        return train_dataset, val_dataset


class UnslothBGLTrainer:
    """Main trainer class for BGL anomaly detection using unsloth."""
    
    def __init__(self, config: TrainingConfig):
        self.config = config
        self.data_processor = BGLDatasetProcessor(config)
        self.model = None
        self.tokenizer = None
    
    def load_model(self):
        """Load the base model and tokenizer."""
        print(f"Loading model: {self.config.model_name}")
        
        self.model, self.tokenizer = FastLanguageModel.from_pretrained(
            model_name=self.config.model_name,
            max_seq_length=self.config.max_seq_length,
            dtype=None,
            load_in_4bit=self.config.load_in_4bit,
        )
        
        # Add LoRA adapters
        self.model = FastLanguageModel.get_peft_model(
            self.model,
            r=self.config.lora_r,
            target_modules=["q_proj", "k_proj", "v_proj", "o_proj",
                          "gate_proj", "up_proj", "down_proj"],
            lora_alpha=self.config.lora_alpha,
            lora_dropout=self.config.lora_dropout,
            bias=self.config.bias,
            use_gradient_checkpointing=self.config.use_gradient_checkpointing,
            random_state=self.config.seed,
            use_rslora=False,
            loftq_config=None,
        )
    
    def format_prompts(self, examples):
        """Format examples for training."""
        texts = []
        for instruction, output in zip(examples["instruction"], examples["output"]):
            text = f"<|begin_of_text|><|start_header_id|>user<|end_header_id|>\n\n{instruction}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n{output}<|eot_id|><|end_of_text|>"
            texts.append(text)
        return {"text": texts}
    
    def train(self, bgl_file_path: str):
        """Main training function."""
        # Process data
        print("Processing BGL data...")
        training_examples = self.data_processor.process_bgl_data(bgl_file_path)
        
        if not training_examples:
            raise ValueError("No training examples created!")
        
        train_dataset, val_dataset = self.data_processor.create_datasets(training_examples)
        
        # Load model
        self.load_model()
        
        # Format datasets
        train_dataset = train_dataset.map(self.format_prompts, batched=True)
        val_dataset = val_dataset.map(self.format_prompts, batched=True)
        
        # Setup trainer
        trainer = SFTTrainer(
            model=self.model,
            tokenizer=self.tokenizer,
            train_dataset=train_dataset,
            eval_dataset=val_dataset,
            dataset_text_field="text",
            max_seq_length=self.config.max_seq_length,
            dataset_num_proc=2,
            packing=False,
            args=TrainingArguments(
                per_device_train_batch_size=self.config.per_device_train_batch_size,
                gradient_accumulation_steps=self.config.gradient_accumulation_steps,
                warmup_steps=self.config.warmup_steps,
                max_steps=self.config.max_steps,
                learning_rate=self.config.learning_rate,
                fp16=self.config.fp16,
                bf16=self.config.bf16,
                logging_steps=self.config.logging_steps,
                optim=self.config.optim,
                weight_decay=self.config.weight_decay,
                lr_scheduler_type=self.config.lr_scheduler_type,
                seed=self.config.seed,
                output_dir=self.config.output_dir,
                evaluation_strategy="steps",
                eval_steps=100,
                save_steps=500,
                save_total_limit=2,
                load_best_model_at_end=True,
                metric_for_best_model="eval_loss",
                greater_is_better=False,
                report_to=None,  # Disable wandb/tensorboard
            ),
        )
        
        # Train the model
        print("Starting training...")
        trainer_stats = trainer.train()
        
        # Save the model
        print(f"Saving model to {self.config.output_dir}")
        trainer.save_model()
        self.tokenizer.save_pretrained(self.config.output_dir)
        
        return trainer_stats


def main():
    """Main function to run the training."""
    config = TrainingConfig()
    trainer = UnslothBGLTrainer(config)
    
    # Path to your BGL data
    bgl_file_path = "datasets/BGL/BGL.log"
    
    if not os.path.exists(bgl_file_path):
        raise FileNotFoundError(f"BGL data file not found: {bgl_file_path}")
    
    # Start training
    stats = trainer.train(bgl_file_path)
    print("Training completed!")
    print(f"Training stats: {stats}")


if __name__ == "__main__":
    main()
