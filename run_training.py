#!/usr/bin/env python3
"""
Simple script to run BGL anomaly detection training with unsloth.

This script provides an easy way to start training with different configurations.
"""

import argparse
import os
import sys
from unsloth_bgl_trainer import UnslothBG<PERSON>rainer, TrainingConfig


def main():
    parser = argparse.ArgumentParser(description="Train BGL anomaly detection model with unsloth")
    
    # Data arguments
    parser.add_argument("--data_path", type=str, default="datasets/BGL/BGL.log",
                       help="Path to BGL log file")
    parser.add_argument("--max_samples", type=int, default=50000,
                       help="Maximum number of training samples")
    parser.add_argument("--window_size", type=int, default=10,
                       help="Size of log windows")
    
    # Model arguments
    parser.add_argument("--model_name", type=str, default="unsloth/llama-3.2-1b-instruct-bnb-4bit",
                       help="Base model to fine-tune")
    parser.add_argument("--max_seq_length", type=int, default=2048,
                       help="Maximum sequence length")
    
    # Training arguments
    parser.add_argument("--max_steps", type=int, default=1000,
                       help="Maximum training steps")
    parser.add_argument("--learning_rate", type=float, default=2e-4,
                       help="Learning rate")
    parser.add_argument("--batch_size", type=int, default=2,
                       help="Training batch size")
    parser.add_argument("--output_dir", type=str, default="./bgl_anomaly_model",
                       help="Output directory for trained model")
    
    # Quick training option
    parser.add_argument("--quick", action="store_true",
                       help="Quick training with reduced parameters for testing")
    
    args = parser.parse_args()
    
    # Check if data file exists
    if not os.path.exists(args.data_path):
        print(f"Error: Data file not found: {args.data_path}")
        sys.exit(1)
    
    # Create training configuration
    config = TrainingConfig()
    
    # Update config with command line arguments
    config.model_name = args.model_name
    config.max_seq_length = args.max_seq_length
    config.max_samples = args.max_samples
    config.window_size = args.window_size
    config.max_steps = args.max_steps
    config.learning_rate = args.learning_rate
    config.per_device_train_batch_size = args.batch_size
    config.output_dir = args.output_dir
    
    # Quick training adjustments
    if args.quick:
        print("Using quick training configuration...")
        config.max_samples = 5000
        config.max_steps = 100
        config.window_size = 5
        config.per_device_train_batch_size = 1
        config.gradient_accumulation_steps = 2
    
    print("Training Configuration:")
    print(f"  Data path: {args.data_path}")
    print(f"  Model: {config.model_name}")
    print(f"  Max samples: {config.max_samples}")
    print(f"  Window size: {config.window_size}")
    print(f"  Max steps: {config.max_steps}")
    print(f"  Learning rate: {config.learning_rate}")
    print(f"  Batch size: {config.per_device_train_batch_size}")
    print(f"  Output dir: {config.output_dir}")
    
    # Create trainer and start training
    trainer = UnslothBGLTrainer(config)
    
    try:
        stats = trainer.train(args.data_path)
        print("\n" + "="*50)
        print("Training completed successfully!")
        print(f"Model saved to: {config.output_dir}")
        print("\nTo test the model, run:")
        print(f"python unsloth_bgl_inference.py")
        
    except Exception as e:
        print(f"\nTraining failed with error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
