"""
Inference script for the trained BGL anomaly detection model using unsloth.

This script loads the trained model and provides functions for classifying
log sequences as normal or anomalous.
"""

import os
import torch
from typing import List, Dict, Tuple
from unsloth import FastLanguageModel
from bgl_processor import BGLProcessor, LogWindow


class BGLAnomalyClassifier:
    """Classifier for BGL log anomaly detection using the trained unsloth model."""
    
    def __init__(self, model_path: str, max_seq_length: int = 2048):
        self.model_path = model_path
        self.max_seq_length = max_seq_length
        self.model = None
        self.tokenizer = None
        self.load_model()
    
    def load_model(self):
        """Load the trained model and tokenizer."""
        print(f"Loading trained model from: {self.model_path}")
        
        self.model, self.tokenizer = FastLanguageModel.from_pretrained(
            model_name=self.model_path,
            max_seq_length=self.max_seq_length,
            dtype=None,
            load_in_4bit=True,
        )
        
        # Enable inference mode
        FastLanguageModel.for_inference(self.model)
        print("Model loaded successfully!")
    
    def create_prompt(self, messages: List[str]) -> str:
        """Create the prompt for inference."""
        log_sequence = " | ".join(messages)
        
        instruction = """You are an expert system administrator analyzing log messages for anomaly detection. 
Analyze the following sequence of log messages and classify it as either "normal" or "anomaly".

Consider these factors:
- Error messages and failure indicators
- Unusual patterns or frequencies
- System component failures
- Network or hardware issues

Log sequence: {log_sequence}

Classification:"""
        
        prompt = f"<|begin_of_text|><|start_header_id|>user<|end_header_id|>\n\n{instruction.format(log_sequence=log_sequence)}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n"
        return prompt
    
    def classify_messages(self, messages: List[str]) -> Tuple[str, float]:
        """
        Classify a list of log messages.
        
        Args:
            messages: List of log message strings
            
        Returns:
            Tuple of (classification, confidence_score)
        """
        if not messages:
            return "normal", 0.0
        
        prompt = self.create_prompt(messages)
        
        # Tokenize input
        inputs = self.tokenizer(
            prompt,
            return_tensors="pt",
            truncation=True,
            max_length=self.max_seq_length
        )
        
        # Generate response
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_new_tokens=10,
                temperature=0.1,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id,
                use_cache=True
            )
        
        # Decode response
        response = self.tokenizer.decode(outputs[0][len(inputs.input_ids[0]):], skip_special_tokens=True)
        response = response.strip().lower()
        
        # Extract classification
        if "anomaly" in response:
            classification = "anomaly"
        elif "normal" in response:
            classification = "normal"
        else:
            # Fallback - if unclear, classify as normal
            classification = "normal"
        
        # Simple confidence score based on response clarity
        confidence = 0.9 if classification in response else 0.5
        
        return classification, confidence
    
    def classify_window(self, window: LogWindow) -> Tuple[str, float]:
        """
        Classify a LogWindow object.
        
        Args:
            window: LogWindow object containing log entries
            
        Returns:
            Tuple of (classification, confidence_score)
        """
        messages = window.get_clean_messages()
        return self.classify_messages(messages)
    
    def batch_classify(self, windows: List[LogWindow]) -> List[Tuple[str, float]]:
        """
        Classify multiple windows in batch.
        
        Args:
            windows: List of LogWindow objects
            
        Returns:
            List of (classification, confidence_score) tuples
        """
        results = []
        for window in windows:
            result = self.classify_window(window)
            results.append(result)
        return results


def evaluate_model(model_path: str, test_data_path: str, num_samples: int = 1000):
    """
    Evaluate the trained model on test data.
    
    Args:
        model_path: Path to the trained model
        test_data_path: Path to BGL test data
        num_samples: Number of samples to evaluate
    """
    print(f"Evaluating model on {num_samples} samples...")
    
    # Load classifier
    classifier = BGLAnomalyClassifier(model_path)
    
    # Load test data
    processor = BGLProcessor()
    windows = processor.create_windows(
        test_data_path,
        window_size=10,
        max_entries=num_samples * 2
    )
    
    # Limit to requested number of samples
    windows = windows[:num_samples]
    
    # Classify
    correct = 0
    total = 0
    true_positives = 0
    false_positives = 0
    true_negatives = 0
    false_negatives = 0
    
    print("Running classification...")
    for window in windows:
        prediction, confidence = classifier.classify_window(window)
        actual = "normal" if window.is_normal else "anomaly"
        
        total += 1
        if prediction == actual:
            correct += 1
        
        # Calculate confusion matrix components
        if actual == "anomaly" and prediction == "anomaly":
            true_positives += 1
        elif actual == "normal" and prediction == "anomaly":
            false_positives += 1
        elif actual == "normal" and prediction == "normal":
            true_negatives += 1
        elif actual == "anomaly" and prediction == "normal":
            false_negatives += 1
    
    # Calculate metrics
    accuracy = correct / total if total > 0 else 0
    precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0
    recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0
    f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
    
    print("\n=== Evaluation Results ===")
    print(f"Total samples: {total}")
    print(f"Accuracy: {accuracy:.4f}")
    print(f"Precision: {precision:.4f}")
    print(f"Recall: {recall:.4f}")
    print(f"F1-Score: {f1:.4f}")
    print(f"\nConfusion Matrix:")
    print(f"True Positives: {true_positives}")
    print(f"False Positives: {false_positives}")
    print(f"True Negatives: {true_negatives}")
    print(f"False Negatives: {false_negatives}")


def demo_classification(model_path: str):
    """
    Demo function showing how to use the classifier.
    """
    print("=== BGL Anomaly Classification Demo ===")
    
    # Load classifier
    classifier = BGLAnomalyClassifier(model_path)
    
    # Example normal log messages
    normal_messages = [
        "RAS KERNEL INFO instruction cache parity error corrected",
        "RAS KERNEL INFO instruction cache parity error corrected",
        "RAS KERNEL INFO data cache parity error corrected"
    ]
    
    # Example anomalous log messages
    anomaly_messages = [
        "RAS APP FATAL ciod: failed to read message prefix on control stream",
        "RAS KERNEL FATAL data storage interrupt",
        "RAS MMCS FATAL connection to service node lost"
    ]
    
    print("\n--- Normal Log Sequence ---")
    print("Messages:", normal_messages)
    classification, confidence = classifier.classify_messages(normal_messages)
    print(f"Classification: {classification} (confidence: {confidence:.2f})")
    
    print("\n--- Anomalous Log Sequence ---")
    print("Messages:", anomaly_messages)
    classification, confidence = classifier.classify_messages(anomaly_messages)
    print(f"Classification: {classification} (confidence: {confidence:.2f})")


def main():
    """Main function for inference testing."""
    model_path = "./bgl_anomaly_model"
    
    if not os.path.exists(model_path):
        print(f"Model not found at {model_path}")
        print("Please train the model first using unsloth_bgl_trainer.py")
        return
    
    # Run demo
    demo_classification(model_path)
    
    # Optionally run evaluation
    test_data_path = "datasets/BGL/BGL.log"
    if os.path.exists(test_data_path):
        print("\n" + "="*50)
        evaluate_model(model_path, test_data_path, num_samples=500)


if __name__ == "__main__":
    main()
