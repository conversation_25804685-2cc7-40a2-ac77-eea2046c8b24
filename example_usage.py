"""
Example Usage and <PERSON> Script for BGL Log Anomaly Detection

This script demonstrates how to use the BGL processor and DSPy agent
for log anomaly detection with both sliding and fixed windows.
"""

import os
import sys
from typing import List
import pandas as pd

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from bgl_processor import BGLProcessor, WindowType, LogWindow
from dspy_anomaly_agent import LogA<PERSON>malyAgent


def demonstrate_bgl_processor():
    """Demonstrate the BGL processor functionality."""
    print("=" * 60)
    print("BGL PROCESSOR DEMONSTRATION")
    print("=" * 60)
    
    # Initialize processor
    dataset_path = "datasets/BGL/BGL.log.deduplicated"
    processor = BGLProcessor(dataset_path)
    
    # Load a subset of the dataset for demonstration
    print("\n1. Loading BGL dataset...")
    processor.load_dataset(max_lines=5000)
    
    # Demonstrate sliding window
    print("\n2. Creating sliding windows...")
    sliding_windows = processor.create_windows(
        window_size=8,
        window_type=WindowType.SLIDING,
        step_size=4
    )
    
    print(f"Created {len(sliding_windows)} sliding windows")
    
    # Show some example windows
    print("\nExample sliding windows:")
    for i, window in enumerate(sliding_windows[:3]):
        print(f"\nWindow {i+1} (ID: {window.window_id}):")
        print(f"  Label: {'Normal' if window.is_normal else 'Abnormal'}")
        print(f"  Range: {window.start_index}-{window.end_index}")
        print(f"  Messages: {' | '.join(window.get_messages()[:2])}...")
    
    # Demonstrate fixed window
    print("\n3. Creating fixed windows...")
    fixed_windows = processor.create_windows(
        window_size=10,
        window_type=WindowType.FIXED
    )
    
    print(f"Created {len(fixed_windows)} fixed windows")
    
    # Show statistics
    print("\n4. Window statistics:")
    stats = processor.get_window_statistics()
    for key, value in stats.items():
        if isinstance(value, float):
            print(f"  {key}: {value:.3f}")
        else:
            print(f"  {key}: {value}")
    
    # Export to DataFrame
    print("\n5. Exporting to DataFrame...")
    df = processor.export_windows_to_dataframe()
    print(f"DataFrame shape: {df.shape}")
    print("\nFirst few rows:")
    print(df[['window_id', 'label', 'start_index', 'end_index']].head())
    
    return processor


def demonstrate_dspy_agent():
    """Demonstrate the DSPy agent functionality."""
    print("\n" + "=" * 60)
    print("DSPY AGENT DEMONSTRATION")
    print("=" * 60)
    
    # Note: This requires an OpenAI API key to be set
    # You can set it as an environment variable: export OPENAI_API_KEY="your-key-here"
    
    try:
        # Initialize agent
        print("\n1. Initializing DSPy agent...")
        dataset_path = "datasets/BGL/BGL.log.deduplicated"
        
        # Check if API key is available
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            print("WARNING: No OpenAI API key found. Set OPENAI_API_KEY environment variable.")
            print("Skipping DSPy agent demonstration.")
            return None
        
        agent = LogAnomalyAgent(
            dataset_path=dataset_path,
            language_model="gpt-3.5-turbo",
            api_key=api_key
        )
        
        # Train the agent
        print("\n2. Training the agent with labeled examples...")
        agent.train(
            window_size=6,
            max_examples=30,  # Small number for demonstration
            balance_classes=True
        )
        
        # Test predictions on sample sequences
        print("\n3. Testing predictions...")
        
        # Normal sequence example (clean messages only)
        normal_sequence = ("instruction cache parity error corrected | "
                          "performing bit sparing on bit 3 | "
                          "generating core.304")

        print(f"\nTesting normal sequence (clean messages):")
        print(f"Input: {normal_sequence}")
        classification, reasoning, confidence = agent.predict(normal_sequence)
        print(f"Classification: {classification}")
        print(f"Reasoning: {reasoning}")
        print(f"Confidence: {confidence:.2f}")

        # Abnormal sequence example (clean messages only)
        abnormal_sequence = ("ciod: failed to read message prefix on control stream | "
                           "FATAL error in application | "
                           "system shutdown initiated")

        print(f"\nTesting abnormal sequence (clean messages):")
        print(f"Input: {abnormal_sequence}")
        classification, reasoning, confidence = agent.predict(abnormal_sequence)
        print(f"Classification: {classification}")
        print(f"Reasoning: {reasoning}")
        print(f"Confidence: {confidence:.2f}")
        
        # Evaluate on test data
        print("\n4. Evaluating on test data...")
        metrics = agent.evaluate_on_test_data(test_samples=50)
        
        print(f"\nEvaluation Results:")
        for metric, value in metrics.items():
            if isinstance(value, float):
                print(f"  {metric}: {value:.3f}")
            else:
                print(f"  {metric}: {value}")
        
        return agent
        
    except Exception as e:
        print(f"Error in DSPy agent demonstration: {e}")
        print("This might be due to missing API key or network issues.")
        return None


def create_sample_predictions():
    """Create sample predictions without requiring API access."""
    print("\n" + "=" * 60)
    print("SAMPLE PREDICTIONS (WITHOUT API)")
    print("=" * 60)
    
    # Load and process some data
    dataset_path = "datasets/BGL/BGL.log.deduplicated"
    processor = BGLProcessor(dataset_path)
    processor.load_dataset(max_lines=1000)
    
    windows = processor.create_windows(window_size=5, window_type=WindowType.SLIDING)
    
    # Get balanced sample
    normal_sample, abnormal_sample = processor.get_balanced_sample(n_samples=10)
    
    print(f"\nSample normal sequences (full messages):")
    for i, window in enumerate(normal_sample[:3]):
        messages = " | ".join(window.get_messages())
        print(f"{i+1}. {messages[:100]}...")

    print(f"\nSample normal sequences (clean messages only):")
    for i, window in enumerate(normal_sample[:3]):
        clean_messages = " | ".join(window.get_clean_messages())
        print(f"{i+1}. {clean_messages[:100]}...")

    print(f"\nSample abnormal sequences (full messages):")
    for i, window in enumerate(abnormal_sample[:3]):
        messages = " | ".join(window.get_messages())
        print(f"{i+1}. {messages[:100]}...")

    print(f"\nSample abnormal sequences (clean messages only):")
    for i, window in enumerate(abnormal_sample[:3]):
        clean_messages = " | ".join(window.get_clean_messages())
        print(f"{i+1}. {clean_messages[:100]}...")
    
    return normal_sample, abnormal_sample


def main():
    """Main function to run all demonstrations."""
    print("BGL Log Anomaly Detection - Example Usage")
    print("This script demonstrates the BGL processor and DSPy agent functionality.")
    
    # Check if dataset exists
    dataset_path = "datasets/BGL/BGL.log.deduplicated"
    if not os.path.exists(dataset_path):
        print(f"ERROR: Dataset not found at {dataset_path}")
        print("Please ensure the BGL dataset is available.")
        return
    
    try:
        # Demonstrate BGL processor
        processor = demonstrate_bgl_processor()
        
        # Demonstrate DSPy agent (requires API key)
        agent = demonstrate_dspy_agent()
        
        # Create sample predictions (no API required)
        normal_sample, abnormal_sample = create_sample_predictions()
        
        print("\n" + "=" * 60)
        print("DEMONSTRATION COMPLETE")
        print("=" * 60)
        
        if agent is None:
            print("\nNote: DSPy agent demonstration was skipped.")
            print("To run the full demonstration, set your OpenAI API key:")
            print("export OPENAI_API_KEY='your-api-key-here'")
        
        print("\nYou can now use the processor and agent in your own code:")
        print("1. Import BGLProcessor for window-based log processing")
        print("2. Import LogAnomalyAgent for DSPy-based anomaly detection")
        print("3. Customize window sizes, step sizes, and training parameters")
        
    except Exception as e:
        print(f"Error during demonstration: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
